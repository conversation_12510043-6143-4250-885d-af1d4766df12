import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '市场经理',
    align:"center",
    dataIndex: 'userName'
  },
  {
    title: '开始时间',
    align:"center",
    dataIndex: 'startTime'
  },
  {
    title: '结束时间',
    align:"center",
    dataIndex: 'endTime'
  },
  {
    title: '拜访类型',
    align:"center",
    dataIndex: 'visitType'
  },
  {
    title: '拜访地址',
    align:"center",
    dataIndex: 'address'
  },
  {
    title: '客户门面照片',
    align:"center",
    dataIndex: 'clientGatePics'
  },
  {
    title: '客户门面地址',
    align:"center",
    dataIndex: 'clientGateAddress'
  },
  {
    title: '客户微信截图',
    align:"center",
    dataIndex: 'wechatScreenshotPics'
  },
  {
    title: '拜访描述',
    align:"center",
    dataIndex: 'interviewDescription'
  },
  {
    title: '拜访记录状态',
    align:"center",
    dataIndex: 'state'
  },
];

//子表列表数据
export const tVisitRecordsPartnerColumns: BasicColumn[] = [
  {
    title: '用户名称',
    align:"center",
    dataIndex: 'userName'
  },
  {
    title: '用户工号',
    align:"center",
    dataIndex: 'userAccount'
  },
  {
    title: '所有者名称',
    align:"center",
    dataIndex: 'ownerName'
  },
];
//子表列表数据
export const tVisitSalesAchievementColumns: BasicColumn[] = [
  {
    title: '产品名称',
    align:"center",
    dataIndex: 'productName'
  },
  {
    title: '产品单位',
    align:"center",
    dataIndex: 'productUnit'
  },
  {
    title: '产品数量',
    align:"center",
    dataIndex: 'productNum'
  },
];
//子表列表数据
export const tVisitRecordsShareColumns: BasicColumn[] = [
  {
    title: '用户名称',
    align:"center",
    dataIndex: 'userName'
  },
  {
    title: '用户工号',
    align:"center",
    dataIndex: 'userAccount'
  },
  {
    title: '分享人工号',
    align:"center",
    dataIndex: 'shareUserAccount'
  },
  {
    title: '分享人名称',
    align:"center",
    dataIndex: 'shareUserName'
  },
];

// 高级查询数据
export const superQuerySchema = {
  userName: {title: '市场经理',order: 1,view: 'text', type: 'string',},
  startTime: {title: '开始时间',order: 2,view: 'datetime', type: 'string',},
  endTime: {title: '结束时间',order: 3,view: 'datetime', type: 'string',},
  visitType: {title: '拜访类型',order: 4,view: 'number', type: 'number',},
  address: {title: '拜访地址',order: 5,view: 'textarea', type: 'string',},
  clientGatePics: {title: '客户门面照片',order: 6,view: 'textarea', type: 'string',},
  clientGateAddress: {title: '客户门面地址',order: 7,view: 'textarea', type: 'string',},
  wechatScreenshotPics: {title: '客户微信截图',order: 8,view: 'textarea', type: 'string',},
  interviewDescription: {title: '拜访描述',order: 9,view: 'textarea', type: 'string',},
  state: {title: '拜访记录状态',order: 10,view: 'number', type: 'number',},
};
