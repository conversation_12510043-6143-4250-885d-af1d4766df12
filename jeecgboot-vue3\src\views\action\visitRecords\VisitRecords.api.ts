import {defHttp} from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/action/visitRecords/list',
  save= '/action/visitRecords/add',
  edit= '/action/visitRecords/edit',
  deleteOne = '/action/visitRecords/delete',
  deleteBatch = '/action/visitRecords/deleteBatch',
  importExcel = '/action/visitRecords/importExcel',
  exportXls = '/action/visitRecords/exportXls',
  tVisitRecordsPartnerList = '/action/visitRecords/listTVisitRecordsPartnerByMainId',
  tVisitRecordsPartnerSave= '/action/visitRecords/addTVisitRecordsPartner',
  tVisitRecordsPartnerEdit= '/action/visitRecords/editTVisitRecordsPartner',
  tVisitRecordsPartnerDelete = '/action/visitRecords/deleteTVisitRecordsPartner',
  tVisitRecordsPartnerDeleteBatch = '/action/visitRecords/deleteBatchTVisitRecordsPartner',
  tVisitSalesAchievementList = '/action/visitRecords/listTVisitSalesAchievementByMainId',
  tVisitSalesAchievementSave= '/action/visitRecords/addTVisitSalesAchievement',
  tVisitSalesAchievementEdit= '/action/visitRecords/editTVisitSalesAchievement',
  tVisitSalesAchievementDelete = '/action/visitRecords/deleteTVisitSalesAchievement',
  tVisitSalesAchievementDeleteBatch = '/action/visitRecords/deleteBatchTVisitSalesAchievement',
  tVisitRecordsShareList = '/action/visitRecords/listTVisitRecordsShareByMainId',
  tVisitRecordsShareSave= '/action/visitRecords/addTVisitRecordsShare',
  tVisitRecordsShareEdit= '/action/visitRecords/editTVisitRecordsShare',
  tVisitRecordsShareDelete = '/action/visitRecords/deleteTVisitRecordsShare',
  tVisitRecordsShareDeleteBatch = '/action/visitRecords/deleteBatchTVisitRecordsShare',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({ url: Api.list, params });

/**
 * 删除单个
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
}

/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    }
  });
}

/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params },{ isTransformResponse: false });
}
  
/**
 * 列表接口
 * @param params
 */
export const tVisitRecordsPartnerList = (params) => {
  if(params['recordsId']){
    return defHttp.get({ url: Api.tVisitRecordsPartnerList, params });
  }
  return Promise.resolve({});
}

/**
 * 删除单个
 */
export const tVisitRecordsPartnerDelete = (params,handleSuccess) => {
  return defHttp.delete({ url: Api.tVisitRecordsPartnerDelete, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
}

/**
 * 批量删除
 * @param params
 */
export const tVisitRecordsPartnerDeleteBatch = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.tVisitRecordsPartnerDeleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    }
  });
}

/**
 * 保存或者更新
 * @param params
 */
export const  tVisitRecordsPartnerSaveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.tVisitRecordsPartnerEdit : Api.tVisitRecordsPartnerSave;
  return defHttp.post({ url: url, params },{ isTransformResponse: false });
}

/**
 * 导入
 */
export const tVisitRecordsPartnerImportUrl = '/action/visitRecords/importTVisitRecordsPartner'

/**
 * 导出
 */
export const tVisitRecordsPartnerExportXlsUrl = '/action/visitRecords/exportTVisitRecordsPartner'
  
/**
 * 列表接口
 * @param params
 */
export const tVisitSalesAchievementList = (params) => {
  if(params['recordsId']){
    return defHttp.get({ url: Api.tVisitSalesAchievementList, params });
  }
  return Promise.resolve({});
}

/**
 * 删除单个
 */
export const tVisitSalesAchievementDelete = (params,handleSuccess) => {
  return defHttp.delete({ url: Api.tVisitSalesAchievementDelete, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
}

/**
 * 批量删除
 * @param params
 */
export const tVisitSalesAchievementDeleteBatch = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.tVisitSalesAchievementDeleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    }
  });
}

/**
 * 保存或者更新
 * @param params
 */
export const  tVisitSalesAchievementSaveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.tVisitSalesAchievementEdit : Api.tVisitSalesAchievementSave;
  return defHttp.post({ url: url, params },{ isTransformResponse: false });
}

/**
 * 导入
 */
export const tVisitSalesAchievementImportUrl = '/action/visitRecords/importTVisitSalesAchievement'

/**
 * 导出
 */
export const tVisitSalesAchievementExportXlsUrl = '/action/visitRecords/exportTVisitSalesAchievement'
  
/**
 * 列表接口
 * @param params
 */
export const tVisitRecordsShareList = (params) => {
  if(params['recordsId']){
    return defHttp.get({ url: Api.tVisitRecordsShareList, params });
  }
  return Promise.resolve({});
}

/**
 * 删除单个
 */
export const tVisitRecordsShareDelete = (params,handleSuccess) => {
  return defHttp.delete({ url: Api.tVisitRecordsShareDelete, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
}

/**
 * 批量删除
 * @param params
 */
export const tVisitRecordsShareDeleteBatch = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.tVisitRecordsShareDeleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    }
  });
}

/**
 * 保存或者更新
 * @param params
 */
export const  tVisitRecordsShareSaveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.tVisitRecordsShareEdit : Api.tVisitRecordsShareSave;
  return defHttp.post({ url: url, params },{ isTransformResponse: false });
}

/**
 * 导入
 */
export const tVisitRecordsShareImportUrl = '/action/visitRecords/importTVisitRecordsShare'

/**
 * 导出
 */
export const tVisitRecordsShareExportXlsUrl = '/action/visitRecords/exportTVisitRecordsShare'
